#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主播稿生成过程中推广文案来源信息和is_adv标识的添加
"""

import json
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_zhubo_generation_with_source_info():
    """测试主播稿生成过程中的来源信息添加"""
    
    print("🧪 测试主播稿生成过程中推广文案来源信息的添加")
    print("=" * 60)
    
    # 模拟AI优化数据结构，包含 generated_content 字段
    mock_ai_data = {
        "optimized_content": [
            {
                "list": {
                    "10.5": "这是第一段优化内容",
                    "15.2": "这是第二段优化内容"
                }
            }
        ],
        "instr": "欢迎大家来到直播间",
        "end": "感谢大家的观看",
        "adv_data": [
            {
                "list": {
                    "20.5": {
                        "txt": "",  # 原始数据中txt为空
                        "is_adv": "1"
                    },
                    "25.8": "这是普通内容",
                    "30.2": {
                        "txt": "",  # 原始数据中txt为空
                        "is_adv": "1"
                    }
                }
            }
        ],
        "generated_content": """[
            {
                "list": {
                    "20.5": {
                        "txt": "这是第一条优化后的推广文案内容，来源于推广文案优化",
                        "is_adv": "1"
                    },
                    "25.8": "这是普通内容",
                    "30.2": {
                        "txt": "这是第二条优化后的推广文案内容，来源于推广文案优化",
                        "is_adv": "1"
                    }
                }
            }
        ]"""
    }
    
    # 导入主播稿处理器
    try:
        from page.txt.zhubo import ZhuboProcessor
        
        # 创建模拟的主控制器
        class MockController:
            def __init__(self):
                self.ai_optimized_data = {}
                
        mock_controller = MockController()
        zhubo_processor = ZhuboProcessor(mock_controller)
        
        print("✅ 成功导入ZhuboProcessor")
        
        # 测试生成主播稿数据
        print("\n📊 测试生成主播稿数据...")
        zhubo_data = zhubo_processor._generate_zhubo_data(mock_ai_data)
        
        print(f"✅ 生成主播稿数据成功，包含 {len(zhubo_data)} 个项目")
        
        # 验证推广文案项目的格式
        print("\n🔍 验证推广文案项目格式...")
        promotion_items = []
        
        for i, item in enumerate(zhubo_data):
            if isinstance(item, dict) and "list" in item:
                project_list = item["list"]
                for timestamp, content in project_list.items():
                    # 检查是否是推广文案格式
                    if isinstance(content, dict) and "txt" in content and "is_adv" in content:
                        promotion_items.append({
                            "项目索引": i,
                            "时间戳": timestamp,
                            "内容": content
                        })
                        
                        # 验证必要字段
                        required_fields = ["txt", "is_adv", "source"]
                        missing_fields = [field for field in required_fields if field not in content]
                        
                        if missing_fields:
                            print(f"❌ 推广文案项目缺少字段: {missing_fields}")
                            print(f"   时间戳: {timestamp}")
                            print(f"   内容: {content}")
                        else:
                            print(f"✅ 推广文案项目格式正确:")
                            print(f"   时间戳: {timestamp}")
                            print(f"   文本: {content['txt']}")
                            print(f"   is_adv: {content['is_adv']}")
                            print(f"   来源: {content['source']}")
                            print()
        
        print(f"📊 找到 {len(promotion_items)} 个推广文案项目")
        
        # 输出完整的主播稿数据结构
        print("\n📄 完整的主播稿数据结构:")
        print(json.dumps(zhubo_data, indent=2, ensure_ascii=False))
        
        # 验证数据结构的完整性
        print("\n🔍 验证数据结构完整性...")
        
        # 检查开场白
        if len(zhubo_data) > 0 and "list" in zhubo_data[0] and "0" in zhubo_data[0]["list"]:
            print("✅ 开场白存在")
        else:
            print("❌ 开场白缺失")
        
        # 检查退场白
        if len(zhubo_data) > 0 and "list" in zhubo_data[-1] and "2000" in zhubo_data[-1]["list"]:
            print("✅ 退场白存在")
        else:
            print("❌ 退场白缺失")
        
        # 检查推广文案是否都包含必要信息
        all_promotion_valid = True
        for item in promotion_items:
            content = item["内容"]
            if not all(field in content for field in ["txt", "is_adv", "source"]):
                all_promotion_valid = False
                break
        
        if all_promotion_valid and len(promotion_items) > 0:
            print("✅ 所有推广文案项目都包含必要的来源信息")
        elif len(promotion_items) == 0:
            print("⚠️ 没有找到推广文案项目")
        else:
            print("❌ 部分推广文案项目缺少必要信息")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_zhubo_generation_with_source_info()
    if success:
        print("\n🎉 测试完成！推广文案来源信息添加功能正常工作")
    else:
        print("\n❌ 测试失败！需要检查代码实现")
